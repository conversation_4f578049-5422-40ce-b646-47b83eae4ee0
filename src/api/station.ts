import type {
  Station,
  StationFindByStationParams,
  StationListStationsParams,
  StationRent,
} from '@/types/api/Station'
import { getInstance } from '@/service'
import { useUserStore } from '@/store/modules/user'

/**
 * @description 根据手机号、身份证号后6位和电站编码获取指定电站租金
 */
export async function getStationRentFindByStation(
  params: StationFindByStationParams,
): Promise<StationRent> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/out/station/rent/find-by-station',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * @description 根据手机号和身份证号后6位获取电站列表
 */
export async function getStationRentListStations(
  params: StationListStationsParams,
): Promise<Station[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/out/station/rent/list-stations',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}
