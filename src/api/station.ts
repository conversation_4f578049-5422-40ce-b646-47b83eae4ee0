import type {
  Station,
  StationFindByStationParams,
  StationListStationsParams,
  StationRent,
} from '@/types/api/Station'
import { merchantInstance } from '@/service'

/**
 * @description 根据手机号、身份证号后6位和电站编码获取指定电站租金
 */
export async function getStationRentFindByStation(
  params: StationFindByStationParams,
): Promise<StationRent> {
  return await merchantInstance.get('/light/operation/out/station/rent/find-by-station', { params })
}

/**
 * @description 根据手机号和身份证号后6位获取电站列表
 */
export async function getStationRentListStations(
  params: StationListStationsParams,
): Promise<Station[]> {
  return await merchantInstance.get('/light/operation/out/station/rent/list-stations', { params })
}
