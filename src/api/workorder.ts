import type { PaginatedContent } from '@/service/types'
import type {
  AssignWorkOrderReq,
  WorkOrder,
  WorkOrderAppealInfo,
  WorkorderAppealListParams,
  WorkorderAppealParams,
  WorkOrderConfig,
  WorkorderConfigPageParams,
  WorkOrderPageParams,
  WorkOrderSubmitReq,
} from '@/types/api/Workorder'
import { getInstance, hdsInstance, merchantInstance } from '@/service'
import { useUserStore } from '@/store'

export async function getWorkOrderPage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/page',
    haier: '/light/operation/workOrder/page',
  }
  const path = urlMap[userType]

  return await getInstance().get(path, { params })
}

export async function getWorkOrderByOrderCode(orderCode: string): Promise<WorkOrder> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
    haier: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
  }
  const path = urlMap[userType]
  return await getInstance().get(path)
}

export async function submitWorkOrder(data: WorkOrderSubmitReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/submit',
    haier: '/light/operation/workOrder/submit',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function auditPassWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/audit-pass`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}

export async function assignWorkOrder(data: AssignWorkOrderReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/assign',
    haier: '/light/operation/workOrder/assign',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderConfigList(
  params: WorkorderConfigPageParams,
): Promise<WorkOrderConfig[]> {
  const path = '/light/operation/work-order-config/list'
  return await hdsInstance.get(path, { params })
}

export async function dispatchWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/dispatch`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}

/**
 * 分页查询已审核的超期申诉工单列表
 */
export function getAuditedAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return hdsInstance.get('/light/operation/workOrder/appeal/audited', { params })
}

/**
 * 根据工单编号查询申诉记录列表
 */
export function getAppealsByOrderCode(
  params: WorkorderAppealListParams,
): Promise<WorkOrderAppealInfo[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/appeal/list',
    haier: '/light/operation/workOrder/appeal/list',
  }
  const path = urlMap[userType]
  return getInstance().get(path, { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getPendingAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return hdsInstance.get('/light/operation/workOrder/appeal/pending', { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getMyAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return merchantInstance.get('/light/operation/workOrder/appeal/my-submitted', { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getMerchantAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return merchantInstance.get('/light/operation/workOrder/appeal/op', { params })
}
