<script setup lang="ts">
import { computed, ref, watch } from 'vue'

const props = defineProps({
  // 图片地址
  src: {
    type: String,
    required: true,
  },
  // referer 地址
  refererUrl: {
    type: String,
    default: '',
  },
  // 强制控制是否使用下载模式
  useDownload: {
    type: Boolean,
    default: null,
  },
})

const emit = defineEmits(['load', 'error'])

let IS_APP_PLUS = false
// #ifdef APP-PLUS
IS_APP_PLUS = true
// #endif

// 下载后的本地路径
const localPath = ref('')

// 判断是否需要启用下载模式
const shouldDownload = computed(() => {
  if (props.useDownload !== null)
    return props.useDownload

  return IS_APP_PLUS && !!props.refererUrl
})

// 最终传递给 wd-img 的 src
const finalSrc = computed(() => {
  return shouldDownload.value ? localPath.value : props.src
})

// 监听 src 的变化，如果需要则下载图片
watch(
  () => props.src,
  (newSrc) => {
    if (shouldDownload.value && newSrc && props.refererUrl) {
      localPath.value = ''
      uni.downloadFile({
        url: newSrc,
        header: { Referer: props.refererUrl },
        success: (res) => {
          if (res.statusCode === 200) {
            localPath.value = res.tempFilePath
          }
          else {
            emit('error', { detail: { errMsg: `Download failed: ${res.statusCode}` } })
          }
        },
        fail: (err) => {
          emit('error', err)
        },
      })
    }
    else {
      localPath.value = ''
    }
  },
  { immediate: true },
)

function onLoad(event: any) {
  emit('load', event)
}
function onError(event: any) {
  emit('error', event)
}
</script>

<template>
  <wd-img :src="finalSrc" v-bind="$attrs" @load="onLoad" @error="onError" />
</template>
