<script setup lang="ts">
import type { RentSearchParams } from '@/types/components/RentSearchForm'
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue?: RentSearchParams
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: RentSearchParams): void
  (e: 'search', value: RentSearchParams): void
}>()

const phone = ref(props.modelValue?.phone || '')
const idCardLast6 = ref(props.modelValue?.idCardLast6 || '')

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    phone.value = newValue.phone || ''
    idCardLast6.value = newValue.idCardLast6 || ''
  }
}, { deep: true })

// 监听内部值变化，同步到外部
watch([phone, idCardLast6], () => {
  const value: RentSearchParams = {
    phone: phone.value,
    idCardLast6: idCardLast6.value,
  }
  emit('update:modelValue', value)
}, { deep: true })

const onSearch = () => {
  const searchParams: RentSearchParams = {
    phone: phone.value,
    idCardLast6: idCardLast6.value,
  }
  emit('search', searchParams)
}

const onReset = () => {
  phone.value = ''
  idCardLast6.value = ''
  const resetParams: RentSearchParams = {
    phone: '',
    idCardLast6: '',
  }
  emit('update:modelValue', resetParams)
  emit('search', resetParams)
}
</script>

<template>
  <view class="rent-search-form">
    <view class="search-form">
      <!-- 手机号码单独一行 -->
      <view class="form-item">
        <text class="label">手机号码</text>
        <wd-input
          :model-value="phone"
          placeholder="请输入手机号码"
          :maxlength="11"
          @update:model-value="(val: string) => phone = val"
        />
      </view>

      <!-- 身份证后6位和查询按钮同一行 -->
      <view class="form-row">
        <view class="form-item form-item--flex">
          <text class="label">身份证后6位</text>
          <wd-input
            :model-value="idCardLast6"
            placeholder="请输入身份证后6位"
            :maxlength="6"
            @update:model-value="(val: string) => idCardLast6 = val"
          />
        </view>
        <view class="button-group">
          <wd-button type="default" class="reset-btn" @click="onReset">重置</wd-button>
          <wd-button type="primary" class="search-btn" @click="onSearch">搜索</wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.rent-search-form {
  padding: 12px;
  background-color: #fff;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;

  &--flex {
    flex: 1;
    margin-right: 12px;
  }
}

.form-row {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.label {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.reset-btn {
  min-width: 60px;
}

.search-btn {
  min-width: 60px;
}
</style>
