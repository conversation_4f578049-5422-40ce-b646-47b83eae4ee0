<script lang="ts" setup>
// 导航栏属性
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showBack: {
    type: Boolean,
    default: true,
  },
  customBack: {
    type: Function,
    default: undefined,
  },
})

// 返回上一页
const handleBack = () => {
  if (props.customBack) {
    props.customBack()
  }
  else {
    uni.navigateBack()
  }
}
</script>

<template>
  <wd-navbar :title="title" fixed :left-arrow="showBack" @click-left="handleBack">
    <template #left>
      <image class="h-[26px] w-[26px]" src="/static/common/arrow-left.svg"></image>
    </template>
    <template #right>
      <slot name="right"></slot>
    </template>
  </wd-navbar>
</template>

<style lang="scss" scoped>
:deep(.wd-navbar__left) {
  padding-left: 5px !important;
}

:deep(.wd-navbar__title) {
  font-size: 16px;
}
</style>
