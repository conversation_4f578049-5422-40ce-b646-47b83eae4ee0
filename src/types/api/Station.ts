/**
 * @description 电站信息
 */
export interface Station {
  /** 详细地址 */
  address?: string
  /** 朝向角度 */
  angle?: string
  /** 组串总数量 */
  assemblyNumber?: number
  /** 银行账户名（业主姓名去掉后缀数字，禁get(id)方法赋值） */
  bankAccountName?: string
  /** 配电箱规格 */
  boxSpecification?: string
  /** 业务类型：1：户用，2：EPC */
  businessType?: number
  /** 市ID */
  cityId?: number
  /** 市 */
  cityName?: string
  /** 工商业项目编码 */
  cmProjectCode?: string
  /** 工商业项目id */
  cmProjectId?: number
  /** 工商业项目名称 */
  cmProjectName?: string
  /** 工商业项目类型 */
  cmProjectType?: string
  /** 装机功率(KW) */
  completeConfirmPower?: number
  /** 装机数量 */
  completeConfirmQuantity?: number
  /** 组件尺寸，如：545/570/580/585W-2278*1134*30，590/620/630/635W-2465*1134*35 */
  componentSize?: string
  /** 数量 */
  count?: number
  /** 创建日期 */
  createdAt?: string
  /** 创建人 */
  createdBy?: string
  /** 配电箱信息 */
  distributionBox?: string
  /** 发电户号 */
  elecNo?: string
  /** 发电户号类型 独立户号电站 or 汇流组共用户号 */
  electricGridType?: string
  /** 质保期结束日期 */
  endWarrantyAt?: string
  /** 电站类型 0-自发自用 余电上网  1-全额上网 */
  epcStationType?: number
  /** 完成时间 */
  finishAt?: string
  /** 首次通电上传发电数据时间 */
  firstLinkAt?: string
  /** 首次连续三天发电时间 */
  firstThreePowerAt?: string
  /** 并网距离(米) */
  grid?: number
  /** 房屋类型 平顶屋flat 斜屋顶slope */
  houseType?: string
  /** 主键ID */
  id?: number
  /** 身份证号 */
  idCardNumber?: string
  /** 逆变器、并网箱安装整体照片 */
  imageUrl1?: string
  /** 电站正面照片 */
  imageUrl2?: string
  /** 电站侧面照片 */
  imageUrl3?: string
  /** 组件倾角 */
  inclinationAngle?: string
  /** 建议安装方式 平顶屋-阳光屋顶 斜屋顶-挂钩固定安装 */
  install?: string
  /** 逆变器型号 */
  inverterSkuName?: string
  /** 逆变器列表 */
  inverters?: Inverter[]
  /** 质保期标识 */
  isWarranty?: string
  /** 纬度 */
  latitude?: number
  /** 经度 */
  longitude?: number
  /** 上网户号 */
  mergeElectricCertNo?: string
  /** 模式，HR:华润，YX:越秀 */
  mode?: string
  /** 组件块数 */
  moduleQuantity?: number
  /** 租户姓名 */
  name?: string
  /** 运维服务商账号 */
  opLoginId?: string
  /** 运维服务商会员id */
  opMemberId?: string
  /** 运维服务商名称 */
  opName?: string
  /** 运维服务商运营中时间 */
  passAt?: string
  /** 联系方式 */
  phone?: string
  /** 功率(KW) */
  planPower?: number
  /** 计划使用数量 */
  planQuantity?: number
  /** 装机功率 */
  power?: number
  /** 项目编码 */
  projectCode?: string
  /** 项目公司代码 */
  projectCompanyCode?: string
  /** 项目公司ID */
  projectCompanyId?: number
  /** 项目公司名称 */
  projectCompanyName?: string
  /** 项目名称 */
  projectName?: string
  /** 项目类型：HOUSEHOLE:户用项目租赁、PUB_BUILD:公共建筑租赁、WHOLE_VILLAGE:整村 */
  projectType?: string
  /** 省ID */
  provinceId?: number
  /** 省 */
  provinceName?: string
  /** 区ID */
  regionId?: number
  /** 区 */
  regionName?: string
  /** 租赁时间 */
  rentAt?: string
  /** 生成业主租金状态:NOT_GENERATED 否 GENERATED 是 */
  rentStatus?: string
  /** 屋顶图片 角度南 */
  roofAngleImage?: string
  /** 屋顶图片 俯拍图app */
  roofHighImage?: string
  /** 屋顶平面图形：RECTANGLE-矩形、CONCAVE_SHAPE-凹字型、L_SHAPE-L型、OTHERS-其它 */
  roofPlanType?: string
  /** 屋顶尺寸照片 */
  roofSizeImage?: string
  /** 变更后屋顶尺寸照片 */
  roofSizeImageChange?: string
  /** 草稿屋顶尺寸照片 */
  roofSizeImageDraft?: string
  /** 光e宝签约状态：NO_SIGN:未签约，SIGNED:已签约 ALLOW_SIGN:可签约 */
  signStatus?: string
  /** 组件规格 */
  sku?: string
  /** 与skuName相同 */
  skuLabel?: string
  /** 组件规格名称 */
  skuName?: string
  /** 服务商Id */
  spId?: number
  /** 服务商账号 */
  spLoginId?: string
  /** 服务商会员id */
  spMemberId?: number
  /** 服务商会员名称 */
  spMemberName?: string
  /** 服务商名称 */
  spName?: string
  /** 特殊标志 */
  specialFlag?: string
  /** 运维服务商的专用时间 */
  specialTime?: string
  /** 电站编码 */
  stationCode?: string
  /** 电站类型：COMMON:普通户用、HOUSEHOLE:户用项目租赁、PUB_BUILD:公共建筑租赁、WHOLE_VILLAGE:整村 */
  stationType?: string
  /** 电站健康度 */
  stationScore?: number
  /** 状态 */
  status?: string
  /** 终止状态：INIT:无,WAIT_AUDIT:待审核,WAIT_SIGN:待签署终止协议,STOP:已终止,AUDIT_REJECT:已驳回,REVOCATION:已撤回 */
  stopStatus?: string
  /** 街道ID */
  streetId?: number
  /** 街道 */
  streetName?: string
  /** 分中心代码 */
  subCenterCode?: string
  /** 分中心名称 */
  subCenterName?: string
  /** 更新日期 */
  updatedAt?: string
  /** 修改人 */
  updatedBy?: string
  /** 业主类型： COM：企业、GOV:政府/事业单位 */
  userType?: string
  vcode?: string
}

/**
 * @description 分页查询电站列表请求参数
 */
export interface StationPageParams {
  /**
   * @description 页码
   */
  pageNum: number
  /**
   * @description 每页条数
   */
  pageSize: number
  /**
   * @description 手机号
   */
  phone?: string
  /**
   * @description 电站编码
   */
  stationCode?: string
  /**
   * @description 户主姓名
   */
  name?: string
}

/**
 * @description 根据电站编码获取电站详情请求参数
 */
export interface StationGetByStationCodeParams {
  /**
   * @description 电站编码
   */
  stationCode: string
}

/**
 * @description 逆变器实时数据
 */
export interface StationInverterRealtimeData {
  /**
   * @description 数据时间
   */
  dataTimestamp?: string
  /**
   * @description 逆变器温度
   */
  inverterTemperature?: number
  /**
   * @description 实时功率
   */
  pac?: number
}

/**
 * @description 获取逆变器实时数据列表请求参数
 */
export interface GetStationInverterDataParams {
  /**
   * @description 查询日期
   */
  date?: string
  /**
   * @description 逆变器SN
   */
  inverterSn?: string
}

/**
 * @description 更新逆变器MPPT信息请求参数
 */
export interface StationInverterMpptUpdateParams {
  /**
   * @description 逆变器ID
   */
  inverterId: number | string
  /**
   * @description MPPT信息(JSON字符串)
   */
  mpptInfo: string
}

/**
 * @description 天气辐射数据
 */
export interface StationWeatherRadiation {
  /**
   * @description 短波辐射(W/m²)
   */
  shortwaveRadiation?: number
  /**
   * @description 数据时间戳
   */
  timestamp?: string
}

/**
 * @description 获取逆变器天气辐射数据请求参数
 */
export interface GetStationInverterWeatherRadiationDataParams {
  /**
   * @description 查询日期
   */
  date?: string
  /**
   * @description 逆变器SN
   */
  inverterSn?: string
}

/**
 * @description 更新电站经纬度请求参数
 */
export interface StationLocationUpdateParams {
  /**
   * @description 新纬度
   */
  newLatitude: number
  /**
   * @description 新经度
   */
  newLongitude: number
  /**
   * @description 电站编码
   */
  stationCode: string
}

/**
 * @description 逆变器发电量数据
 */
export interface StationInverterPowerData {
  /**
   * @description 日等效小时数
   */
  dailyEquivalentHours?: number
  /**
   * @description 数据更新时间
   */
  dataTimeAt?: string
  /**
   * @description 日发电量(kWh)
   */
  elecDay?: number
  /**
   * @description 月发电量(kWh)
   */
  elecMonth?: number
  /**
   * @description 累计发电量(kWh)
   */
  elecTotal?: number
  /**
   * @description 年发电量(kWh)
   */
  elecYear?: number
  /**
   * @description 逆变器状态，1：在线 2：离线 3：报警
   */
  inveterState?: number
  /**
   * @description 实时功率(KW)
   */
  pac?: number
  /**
   * @description 装机容量(KW)
   */
  power?: number
}

/**
 * @description 获取逆变器发电量数据请求参数
 */
export interface GetStationInverterElecDataParams {
  /**
   * @description 逆变器SN
   */
  inverterSn: string
}

/**
 * @description 逆变器信息
 */
export interface Inverter {
  id?: string | number
  stationId?: number
  stationCode?: string
  name?: string
  brandName?: string
  imageUrl?: string
  inverterModel?: string
  inverterSn?: string
  status?: string
  mpptInfo?: string
  createdAt?: string
  updatedAt?: string
  updatedBy?: string
}

/**
 * @description 获取电站逆变器列表请求参数
 */
export interface GetStationInverterListParams {
  /**
   * @description 电站编码
   */
  stationCode: string
}

/**
 * @description 逆变器MPPT数据
 */
export interface InverterMpptData {
  /**
   * @description 时间
   */
  dataTimestamp: string
  /**
   * @description MPPT名称
   */
  mpptName: string
  /**
   * @description 功率
   */
  power: number
}

/**
 * @description 获取逆变器MPPT数据列表请求参数
 */
export interface GetStationInverterMpptDataParams {
  /**
   * @description 查询日期
   */
  date?: string
  /**
   * @description 逆变器SN
   */
  inverterSn?: string
}

/**
 * @description 电站租金查询返回结果
 */
export interface StationRent {
  /**
   * 账户名称
   */
  accountName?: string
  /**
   * 银行卡号(脱敏处理)
   */
  bankCardNumber?: string
  /**
   * 账单日期
   */
  billDate?: string
  /**
   * 新发电户号
   */
  elecNo?: string
  /**
   * 电费单价(元/kWh)
   */
  elecPrice?: number
  /**
   * 个人实收金额(元)
   */
  personalActualAmount?: number
  /**
   * 手机号码
   */
  phone?: string
  /**
   * 发电量(kWh)
   */
  elec?: number
  /**
   * 项目公司
   */
  projectCompanyName?: string
  /**
   * 应收电费(元)
   */
  paymentMoney?: number
  /**
   * 结算时间
   */
  settlementDate?: string
  /**
   * 电站地址
   */
  stationAddress?: string
  /**
   * 电站编码
   */
  stationCode?: string
  /**
   * 电站照片
   */
  stationImg?: string
  /**
   * 租户姓名
   */
  tenantName?: string
  /**
   * 支付时间
   */
  paymentTime?: string
}

/**
 * @description 根据手机号、身份证号后6位和电站编码获取指定电站租金请求参数
 */
export interface StationFindByStationParams {
  /**
   * @description 身份证号后6位
   */
  idCardLast6: string
  /**
   * @description 查询月份(格式：YYYY-MM)，为空时默认查询上个月
   */
  month?: string
  /**
   * @description 手机号
   */
  phone: string
  /**
   * @description 电站编码
   */
  stationCode: string
}

/**
 * @description 根据手机号和身份证号后6位获取电站列表请求参数
 */
export interface StationListStationsParams {
  /**
   * @description 身份证号后6位
   */
  idCardLast6: string
  /**
   * @description 手机号
   */
  phone: string
}
