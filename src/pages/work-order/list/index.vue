<script setup lang="ts">
import type { WorkOrder, WorkOrderPageParams } from '@/types/api/Workorder'
import { getWorkOrderPage } from '@/api/workorder'
import StationCard from './components/StationCard.vue'

const keyword = ref('')
const currentFilters = ref({})

const paging = ref<ZPagingRef>()
const orderList = ref<WorkOrder[]>([])
const pageSize = ref(20)

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: WorkOrderPageParams = {
    pageNum,
    pageSize,
  }

  if (keyword.value) {
    if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    }
    else {
      params.stationName = keyword.value
    }
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  try {
    const res = await getWorkOrderPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  }
  catch {
    paging.value?.complete(false)
  }
}

const handleWorkOrderUpdated = () => {
  if (paging.value) {
    paging.value.reload()
  }
}

const goToCreate = () => {
  uni.navigateTo({
    url: `/pages/work-order/create`,
    events: {
      workOrderUpdated: handleWorkOrderUpdated,
    },
  })
}
</script>

<template>
  <view class="work-order-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="false"
      placeholder="请输入电站编号/电站名称"
      @search="onSearch"
    />

    <!-- 工单列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="order-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="order-list-content">
        <StationCard
          v-for="order in orderList"
          :key="order.id"
          :order="order"
        />
      </view>
    </z-paging>
    <view class="create-btn">
      <wd-button
        type="primary"
        :round="false"
        @click="goToCreate"
      >
        创建工单
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "工单列表"
  }
}
</route>

<style scoped lang="scss">
.work-order-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .order-list-paging {
    flex: 1;
  }

  .order-list-content {
    padding: 8px 12px;
  }

  .order-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        flex: 1;
        margin-right: 4px;
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-meta {
      padding-bottom: 8px;
      margin-bottom: 10px;
      font-size: 12px;
      color: #999;
      border-bottom: 1px solid #f0f0f0;
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
