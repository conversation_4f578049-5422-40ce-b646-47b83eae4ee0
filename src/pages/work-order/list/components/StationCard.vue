<script setup lang="ts">
import type { WorkOrder } from '@/types/api/Workorder'
import { useDictStore } from '@/store/modules/dict'
import { desensitizePhone } from '@/utils/desensitize'
import { onMounted } from 'vue'

const { order } = withDefaults(
  defineProps<{
    order: WorkOrder
    isAppeal?: boolean
  }>(),
  {
    isAppeal: false,
  },
)

const dictStore = useDictStore()

const getOrderStatusText = (status?: string) => {
  return dictStore.getDictLabel('work_order_status', status)
}

const getOrderAppealStatusText = (status?: string) => {
  if (!status) {
    return ''
  }
  const statusMap: Record<string, string> = {
    WAIT_AUDIT: '待审核',
    AUDIT_OK: '已通过',
    AUDIT_REJECT: '已驳回',
  }
  return statusMap[status] || status
}

const getOrderAppealStatusType = (status?: string): any => {
  if (!status) {
    return 'primary'
  }
  const statusMap: Record<string, string> = {
    WAIT_AUDIT: 'primary',
    AUDIT_OK: 'success',
    AUDIT_REJECT: 'danger',
  }
  return statusMap[status] || status
}

onMounted(() => {
  dictStore.fetchDict('work_order_status')
})
</script>

<template>
  <view class="order-card">
    <view class="card-header">
      <text class="title">{{ order.orderName }}</text>
      <wd-tag v-if="!isAppeal" type="primary">{{ getOrderStatusText(order.orderStatus) }}</wd-tag>
      <wd-tag v-else :type="getOrderAppealStatusType(order.appealStatus)">{{ getOrderAppealStatusText(order.appealStatus) }}</wd-tag>
    </view>
    <view class="card-meta">
      <text>{{ order.createdAt }}</text>
    </view>
    <view class="card-body">
      <view class="detail-item">
        <text class="label">电站编码</text>
        <text class="value">{{ order.stationCode }}</text>
      </view>
      <view class="detail-item">
        <text class="label">电站名称</text>
        <text class="value">{{ order.stationName }}</text>
      </view>
      <view class="detail-item">
        <text class="label">联系方式</text>
        <text class="value">{{ desensitizePhone(order.stationPhone) }}</text>
      </view>
      <view class="detail-item">
        <text class="label">电站地址</text>
        <text class="value">{{ order.address }}</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.order-card {
  padding: 12px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      flex: 1;
      margin-right: 4px;
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }

  .card-meta {
    padding-bottom: 8px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #999;
    border-bottom: 1px solid #f0f0f0;
  }

  .card-body {
    .detail-item {
      display: flex;
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 1.6;

      .label {
        flex-shrink: 0;
        width: 70px;
        margin-right: 8px;
        color: #666;
      }

      .value {
        color: #333;
        word-break: break-all;
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
