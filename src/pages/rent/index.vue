<script lang="ts" setup>
import type { Station, StationListStationsParams } from '@/types/api/Station'
import type { RentSearchParams } from '@/types/components/RentSearchForm'
// ZPagingRef 类型通过全局类型定义提供
import { getStationRentListStations } from '@/api/station'
import RentSearchForm from '@/components/RentSearchForm.vue'
import StationCard from '@/components/StationCard.vue'
import { navigateTo } from '@uni-helper/uni-promises'
import { reactive, ref } from 'vue'

const searchParams = ref<RentSearchParams>({
  phone: '',
  idCardLast6: '',
})

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pagnation = reactive({
  pageSize: 100,
  total: 0,
})

const onSearch = (params: RentSearchParams) => {
  searchParams.value = params
  paging.value?.reload()
}

const queryList = async () => {
  const params: StationListStationsParams = {
    phone: searchParams.value.phone,
    idCardLast6: searchParams.value.idCardLast6,
  }

  try {
    const res = await getStationRentListStations(params)
    paging.value?.completeByTotal(res, res.length)
  }
  catch {
    paging.value?.complete(false)
  }
}

const goToDetail = (item: Station) => {
  navigateTo({
    url: `/pages/rent/detail?stationCode=${item.stationCode}`,
  })
}
</script>

<template>
  <view class="monitor-page">
    <!-- 搜索表单组件 -->
    <RentSearchForm
      v-model="searchParams"
      @search="onSearch"
    />

    <!-- 电站列表 -->
    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      :default-page-size="pagnation.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="station-list-content">
        <station-card
          v-for="item in stationList"
          :key="item.id || item.stationCode"
          :item="item"
          @click="goToDetail"
        />
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "租金查询"
  }
}
</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .station-list-paging {
    flex: 1;
  }

  .station-list-content {
    padding: 8px 12px;
  }
}
</style>
