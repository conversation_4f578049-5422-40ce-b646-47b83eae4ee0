<script lang="ts" setup>
import type { Station, StationListStationsParams } from '@/types/api/Station'
import { getStationRentListStations } from '@/api/station'
import StationCard from '@/pages/station/components/StationCard.vue'
import { navigateTo } from '@uni-helper/uni-promises'
import { reactive, ref } from 'vue'

const phone = ref('')
const idCardLast6 = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pagnation = reactive({
  pageSize: 100,
  total: 0,
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async () => {
  const params: StationListStationsParams = {
    phone: phone.value,
    idCardLast6: idCardLast6.value,
  }

  try {
    const res = await getStationRentListStations(params)
    paging.value?.completeByTotal(res, res.length)
  }
  catch {
    paging.value?.complete(false)
  }
}

const goToDetail = (item: Station) => {
  navigateTo({
    url: `/pages/rent/detail?stationCode=${item.stationCode}`,
  })
}
</script>

<template>
  <view class="monitor-page">
    <view class="search-section">
      <view class="search-form">
        <view class="form-item">
          <text class="label">手机号码</text>
          <wd-input v-model="phone" placeholder="请输入手机号码" type="number" maxlength="11" />
        </view>
        <view class="form-item">
          <text class="label">身份证后6位</text>
          <wd-input v-model="idCardLast6" placeholder="请输入身份证后6位" maxlength="6" />
        </view>
        <wd-button type="primary" class="search-btn" @click="onSearch">搜索</wd-button>
      </view>
    </view>

    <!-- 电站列表 -->
    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      :default-page-size="pagnation.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryList"
    >
      <view class="station-list-content">
        <station-card
          v-for="item in stationList"
          :key="item.id || item.stationCode"
          :item="item"
          @click="goToDetail"
        />
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "租金查询"
  }
}
</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .search-section {
    padding: 12px;
    background-color: #fff;
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .label {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
  }

  .search-btn {
    margin-top: 8px;
  }

  .station-list-paging {
    flex: 1;
  }

  .station-list-content {
    padding: 8px 12px;
  }
}
</style>
