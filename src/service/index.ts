import type { VueQueryPluginOptions } from '@tanstack/vue-query'
import type { UnConfigExtend, UnInstance, UnResponse } from '@uni-helper/uni-network'
import type { ApiResponse } from './types'

import { DefaultHeaders, HDSBaseUrl, MerchantBaseUrl } from '@/constants'
import { useAuthStore, useUserStore } from '@/store'

import { MutationCache, QueryCache, QueryClient } from '@tanstack/vue-query'
import un from '@uni-helper/uni-network'
import { showNetworkError } from './helper'

function createInstance(baseUrl: string) {
  const newInstance = un.create({
    baseUrl,
    timeout: 30_000,
  }) as UnInstance

  newInstance.interceptors.request.use((config: UnConfigExtend) => {
    const authStore = useAuthStore()
    config.headers = {
      ...DefaultHeaders,
      Authorization: `Bearer ${authStore.token}`,
      ...config.headers,
    }
    if (config.params && Object.keys(config.params).length > 0) {
      const newParams: Record<string, any> = {}
      for (const key in config.params) {
        if (
          Object.prototype.hasOwnProperty.call(config.params, key)
          && config.params[key] !== undefined
        ) {
          newParams[key] = config.params[key]
        }
      }
      config.params = {
        ...newParams,
      }
    }
    return config
  })

  newInstance.interceptors.response.use(
    (response: UnResponse): UnResponse | Promise<UnResponse> => {
      const resData = response.data as ApiResponse
      if (resData.success) {
        return resData.result
      }
      else {
        uni.showToast({
          title: resData.error || '请求失败',
          icon: 'none',
        })
        return Promise.reject(resData.error || '请求失败')
      }
    },
  )

  return newInstance
}

export const hdsInstance = createInstance(HDSBaseUrl)
export const merchantInstance = createInstance(MerchantBaseUrl)
export function getInstance() {
  const { userInfo } = useUserStore()
  if (userInfo?.userType === 'haier') {
    return hdsInstance
  }
  else {
    return merchantInstance
  }
}

export const vueQueryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      if (un.isCancel(error))
        return
      showNetworkError({ error: error as IUnError })
    },
  }),
  mutationCache: new MutationCache({
    onError: (error) => {
      if (un.isCancel(error))
        return
      showNetworkError({ error: error as IUnError })
    },
  }),
})

export const vueQueryPluginOptions: VueQueryPluginOptions = {
  queryClient: vueQueryClient,
}
