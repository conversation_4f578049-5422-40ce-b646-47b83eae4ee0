// 电站模式
export const detailMode: Record<string, any> = {
  HR: 'BT',
  YX: '户用EPC(YX)',
  ZH: '户用EPC(ZH)',
  CHD_EPC: '户用EPC(HD)',
  EPC: '工商业EPC',
}
export const pmDetailMode = [
  {
    value: 'HR', // 华润
    label: 'BT',
  },
  {
    value: 'YX', // 越秀
    label: '户用EPC(YX)',
  },
  {
    value: 'ZH', // 中核
    label: '户用EPC(ZH)',
  },
  {
    value: 'ZCHD_EPC', // 华电
    label: '户用EPC(HD)',
  },
  {
    value: 'EPC', // 工商业EPC
    label: '工商业EPC',
  },
]
/***
 * @description: 工单状态
 */
export const OperSatusArr: Record<string, any> = {
  WAIT_DISPATCH: '已派单',
  DISPATCHED: '已派工',
  SERVICE: '服务中',
  COMPLETE: '已完工',
  CLOSED: '已关闭',
}
export const operSatusArr = [
  {
    value: 'WAIT_DISPATCH',
    label: '已派单',
  },
  {
    value: 'DISPATCHED',
    label: '已派工',
  },
  {
    value: 'SERVICE',
    label: '服务中',
  },
  {
    value: 'COMPLETE',
    label: '已完工',
  },
  {
    value: 'CLOSED',
    label: '已关闭',
  },
]
/***
 * @description: 工单流程来源
 */
export const recordSource: Record<string, any> = {
  MERCHANT: '运维商',
  MINI_PROGRAM: '运维商',
  HDS: '总部',
}
/***

 * @description: 签约管理-服务商类型
 */
export const identityType: Record<string, any> = {
  STATION_SERVICE: '建站服务商',
  THIRD_SERVICE: '专业运维商',
}
/****
 * @description: 月份
 */
export const monthList = [
  {
    value: '1',
    label: '一月',
  },
  {
    value: '2',
    label: '二月',
  },
  {
    value: '3',
    label: '三月',
  },
  {
    value: '4',
    label: '四月',
  },
  {
    value: '5',
    label: '五月',
  },
  {
    value: '6',
    label: '六月',
  },
  {
    value: '7',
    label: '七月',
  },
  {
    value: '8',
    label: '八月',
  },
  {
    value: '9',
    label: '九月',
  },
  {
    value: '10',
    label: '十月',
  },
  {
    value: '11',
    label: '十一月',
  },
  {
    value: '12',
    label: '十二月',
  },
]

export const detStationType: Record<string, any> = {
  COMMON: '普通户用',
  HOUSEHOLD: '户用租赁',
  PUB_BUILD: '公共租赁',
  WHOLE_VILLAGE: '整村推进',
}
export const detFieldMethod: Record<string, any> = {
  COMPANY: '公司备案',
  HOUSEHOLD: '户用备案',
}
export const opType: Record<string, any> = {
  STATION_SERVICE: '建站服务商',
  THIRD_SERVICE: '专业运维服务商',
}
export const subCenterList = [
  {
    value: 'GFBJ',
    label: '北京',
  },
  {
    value: 'GFCC',
    label: '长春',
  },
  {
    value: 'GFCD',
    label: '成都',
  },
  {
    value: 'GFCQ',
    label: '重庆',
  },
  {
    value: 'GFCS',
    label: '长沙',
  },
  {
    value: 'GFDL',
    label: '大连',
  },
  {
    value: 'GFFZ',
    label: '福州',
  },
  {
    value: 'GFGY',
    label: '贵阳',
  },
  {
    value: 'GFGZ',
    label: '广州',
  },
  {
    value: 'GFHEB',
    label: '哈尔滨',
  },
  {
    value: 'GFHF',
    label: '合肥',
  },
  {
    value: 'GFHHHT',
    label: '内蒙',
  },
  {
    value: 'GFHK',
    label: '海口',
  },
  {
    value: 'GFHZ',
    label: '杭州',
  },
  {
    value: 'GFJIN',
    label: '济宁',
  },
  {
    value: 'GFJN',
    label: '济南',
  },
  {
    value: 'GFJZ',
    label: '锦州',
  },
  {
    value: 'GFKM',
    label: '昆明',
  },
  {
    value: 'GFLZ',
    label: '兰州',
  },
  {
    value: 'GFNB',
    label: '宁波',
  },
  {
    value: 'GFNC',
    label: '南昌',
  },
  {
    value: 'GFNJ',
    label: '南京',
  },
  {
    value: 'GFNN',
    label: '南宁',
  },
  {
    value: 'GFQD',
    label: '青岛',
  },
  {
    value: 'GFSH',
    label: '上海',
  },
  {
    value: 'GFSJZ',
    label: '石家庄',
  },
  {
    value: 'GFSY',
    label: '沈阳',
  },
  {
    value: 'GFSZ',
    label: '深圳',
  },
  {
    value: 'GFTJ',
    label: '天津',
  },
  {
    value: 'GFTS',
    label: '唐山',
  },
  {
    value: 'GFTY',
    label: '太原',
  },
  {
    value: 'GFWH',
    label: '武汉',
  },
  {
    value: 'GFWX',
    label: '无锡',
  },
  {
    value: 'GFXA',
    label: '西安',
  },
  {
    value: 'GFXF',
    label: '襄樊',
  },
  {
    value: 'GFXJ',
    label: '新疆',
  },
  {
    value: 'GFXM',
    label: '厦门',
  },
  {
    value: 'GFXN',
    label: '西宁',
  },
  {
    value: 'GFXZ',
    label: '徐州',
  },
  {
    value: 'GFYC',
    label: '银川',
  },
  {
    value: 'GFYT',
    label: '烟台',
  },
  {
    value: 'GFZZ',
    label: '郑州',
  },
]
export const businessType: Record<string, any> = {
  1: '户用',
  2: '工商业运维',
  3: '工商业运维',
}
export const businessData = [
  {
    value: '1',
    label: '户用',
  },
  {
    value: '2,3',
    label: '工商业运维',
  },
]
/****
 * @description: 电站类型
 */
export const stationTypeList = [
  {
    value: 'COMMON',
    label: '普通户用',
  },
  {
    value: 'HOUSEHOLD',
    label: '户用租赁',
  },
  {
    value: 'PUB_BUILD',
    label: '公共租赁',
  },
  {
    value: 'WHOLE_VILLAGE',
    label: '整村推进',
  },
]
export const pmDetFieldMethod = [
  {
    value: 'COMPANY',
    label: '公司备案',
  },
  {
    value: 'HOUSEHOLD',
    label: '户用备案',
  },
]

export const pmStationTypeList = [
  {
    value: 'COMMON',
    label: '普通户用',
  },
  {
    value: 'HOUSEHOLD',
    label: '户用租赁',
  },
  {
    value: 'PUB_BUILD',
    label: '公共租赁',
  },
  {
    value: 'WHOLE_VILLAGE',
    label: '整村推进',
  },
  {
    value: 'CM',
    label: '工商业',
  },
]
export const pmDetStationType: Record<string, any> = {
  COMMON: '普通户用',
  HOUSEHOLD: '户用租赁',
  PUB_BUILD: '公共租赁',
  WHOLE_VILLAGE: '整村推进',
  CM: '工商业',
}
export const pmSpecialFlag: Record<string, any> = {
  PF_1th: '浦银',
  PY_ALL: '浦银EPC',
  ZH_1th: '中核股转',
  ZH_ALL: '中核EPC',
  YX_EPC: '越秀',
  YX_ALL: '越秀EPC',
  // YX_EPC1: "越秀EPC",
  YX_GZ: '越秀股转',
  CHD_EPC: '华电',
  NH: '纳晖',
  CLIENT_BUY_BACK: '客户回购',
  PF: '浦银',
  DH_EPC: '顶好',
  ZH_PHASE_1: '中核一期',
  ZH_PHASE_2: '中核二期',
}
export const pmSpecialFlag2: Record<string, any> = {
  PF_1th: '浦银',
  PF_2th: '浦银EPC',
  PF_3th: '浦银EPC',
  PF_4th: '浦银EPC',
  PF_5th: '浦银EPC',
  PF_6th: '浦银EPC',
  PF_7th: '浦银EPC',
  PF_8th: '浦银EPC',
  PF_9th: '浦银EPC',
  PF_10th: '浦银EPC',
  PF_11th: '浦银EPC',
  PF_12th: '浦银EPC',
  PF_13th: '浦银EPC',
  PF_14th: '浦银EPC',
  PF_15th: '浦银EPC',
  PF_16th: '浦银EPC',
  ZH_1th: '中核股转',
  ZH_PHASE_1: '中核一期',
  ZH_PHASE_2: '中核二期',
  ZH_EPC_1th: '中核EPC',
  ZH_EPC: '中核EPC',
  YX_EPC: '越秀',
  YX_GZ: '越秀股转',
  YX_EPC1: '越秀EPC',
  DH_EPC: '顶好',
  CHD_EPC: '华电',
  NH: '纳晖',
  CLIENT_BUY_BACK: '客户回购',
  NOT_DIS: '--',
}
export const pmHouseTypeList: Record<string, any> = {
  flat: '平顶屋',
  slope: '斜顶屋',
  ground: '院内地面',
  hwdm: '户外地面',
  yghb: '渔光互补',
  nghb: '农光互补',
  ygf: '阳光房',
  epcSteel: '彩钢瓦',
  epcBeton: '混凝土',
  epcFloor: '地面',
  double_slope: '双面坡',
  enterprise_one: '企业厂房-彩钢瓦屋顶',
}
export const pmInstallList: Record<string, any> = {
  epcFixture: '夹具',
  epcGroundSupport: '地面支架',
  epcPrefabricate: '预制基础',
  enterprise_one_install: '厂房-彩钢瓦-平铺',
  flatSun: '平顶屋-阳光屋顶',
  flatToBolt: '平屋顶-平改坡（膨胀螺栓固定）',
  flatToBase: '平屋顶-平改坡（底梁固定）',
  flatToWeight: '平屋顶-平改坡（配重块）',
  flatBolt: '平屋顶-膨胀螺栓固定',
  flatWeight: '平屋顶-配重块',
  groundPileFix: '院内地面-柱桩固定',
  waterFloating: '水面漂浮',
  pileFilx: '柱桩固定',
  slopePull: '斜屋顶-前拉后拽',
  slopePullShort: '斜屋顶-前拉后拽+短柱',
  sunlightRoomFix: '阳光房-化学锚栓固定/膨胀螺栓',
  slopeHook: '斜屋顶-挂钩固定安装',
  slope_one: '双面坡-南北坡-前拉后拽',
  slope_two: '双面坡-南北坡-前拉后拽+短柱',
  slope_three: '双面坡-南北坡-双坡挂钩',
  slope_four: '双面坡-南北坡-双坡挂钩+水槽',
  slope_five: '双面坡-东西坡-前拉后拽',
  slope_six: '双面坡-东西坡-前拉后拽+短柱',
  slope_seven: '双面坡-东西坡-双坡挂钩',
  slope_eight: '双面坡-东西坡-双坡挂钩+水槽',
}
export const contractType = [
  {
    value: 'DOUBLE',
    label: '双方合同',
  },
  {
    value: 'THREE',
    label: '三方合同',
  },
  {
    value: 'MORE',
    label: '多方合同',
  },
]
export const contractStatus: Record<string, any> = {
  DOUBLE: '双方合同',
  THREE: '三方合同',
  MORE: '多方合同',
}
export const property = [
  {
    value: 'PF_1th',
    label: '浦银',
  },
  {
    value: 'PY_ALL',
    label: '浦银EPC',
  },
  {
    value: 'ZH_1th',
    label: '中核股转',
  },
  {
    value: 'ZH_ALL',
    label: '中核EPC',
  },
  {
    value: 'YX_EPC',
    label: '越秀',
  },
  {
    value: 'YX_ALL',
    label: '越秀EPC',
  },
  {
    value: 'YX_GZ',
    label: '越秀股转',
  },
  {
    value: 'NH',
    label: '纳晖',
  },
  {
    value: 'CHD_EPC',
    label: '华电',
  },
  {
    value: 'CLIENT_BUY_BACK',
    label: '客户回购',
  },
  {
    value: 'NH',
    label: '纳晖',
  },
  {
    value: 'PF',
    label: '浦银',
  },
  {
    value: 'DH_EPC',
    label: '顶好',
  },
  {
    value: 'ZH_PHASE_1',
    label: '中核一期',
  },
  {
    value: 'ZH_PHASE_2',
    label: '中核二期',
  },
]
/***
 * @description: 运维收入成本配置安装类型
 */
export const rcType: Record<string, any> = {
  ZLS: '阵列式',
  GSY: '工商业',
  SMP: '双面坡',
  TYS: '庭院式',
  YGF: '阳光房',
}
export const rcTypeList = [
  {
    value: 'ZLS',
    label: '阵列式',
  },
  {
    value: 'GSY',
    label: '工商业',
  },
  {
    value: 'SMP',
    label: '双面坡',
  },
  {
    value: 'TYS',
    label: '庭院式',
  },
  {
    value: 'YGF',
    label: '阳光房',
  },
]
export const ospBusinessType: Record<string, any> = {
  1: '户用',
  2: '工商业',
}
export const ospBusinessTypeList = [
  {
    value: 1,
    label: '户用',
  },
  {
    value: 2,
    label: '工商业',
  },
]
